<template>
	<div class="flex h-full bg-white dark:bg-[#101014]">
		<!-- 左侧栏 -->
		<div class="w-80 border-r border-gray-200 dark:border-gray-700 flex flex-col">
			<!-- 新建审查清单按钮 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<button
					class="w-full px-4 py-3 rounded-lg text-white font-medium transition-all duration-300 hover:shadow-lg hover:scale-105 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
					@click="handleCreateChecklist">
					<div class="flex items-center justify-center space-x-2">
						<NIcon size="18">
							<AddOutline />
						</NIcon>
						<span>新建审查清单</span>
					</div>
				</button>
			</div>

			<!-- 搜索框 -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<NInput v-model:value="searchValue" placeholder="搜索清单列表" clearable>
					<template #prefix>
						<NIcon>
							<SearchOutline />
						</NIcon>
					</template>
				</NInput>
			</div>

			<!-- 树形列表 -->
			<div class="flex-1 overflow-y-auto tree-container">
				<NTree :data="filteredTreeData" :selected-keys="selectedKeys" :expanded-keys="expandedKeys" selectable
					@update:selected-keys="handleSelectNode" @update:expanded-keys="handleExpandNode" />
			</div>
		</div>

		<!-- 右侧内容区 -->
		<div class="flex-1 flex flex-col h-full">
			<!-- 空状态 -->
			<div v-if="!selectedChecklist" class="flex-1 flex items-center justify-center text-gray-500">
				<div class="text-center">
					<NIcon size="48" class="mb-4">
						<DocumentTextOutline />
					</NIcon>
					<div>请从左侧列表选择清单</div>
				</div>
			</div>

			<!-- 选中清单内容 -->
			<div v-else class="flex-1 flex flex-col h-full min-h-0">
				<!-- 标题区 -->
				<div class="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-start flex-shrink-0">
					<div class="flex-1">
						<div class="flex items-center space-x-2 mb-2">
							<h1 class="text-2xl font-bold">
								{{ selectedChecklist.title }}
							</h1>
							<NBadge :value="selectedChecklist.status === 'published' ? '已发布' : '停用'
								" :type="selectedChecklist.status === 'published'
									? 'success'
									: 'warning'
									" />
						</div>
						<div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
							<span class="font-medium">清单名称：</span>{{ selectedChecklist.name }}
						</div>
						<div class="text-sm text-gray-600 dark:text-gray-400">
							<span class="font-medium">清单描述：</span>{{ selectedChecklist.description }}
						</div>
					</div>

					<!-- 已发布状态的操作按钮 -->
					<div v-if="selectedChecklist.status === 'published'" class="flex items-center space-x-3">
						<div class="flex items-center space-x-2">
							<label class="text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">审查立场：</label>
							<NSelect v-model:value="reviewStance" :options="reviewStanceOptions" placeholder="选择审查立场" class="w-40" />
						</div>
						<div class="flex space-x-2">
							<NButton type="primary">发起审查</NButton>
							<NButton @click="handleEditChecklist">编辑清单</NButton>
						</div>
					</div>

					<!-- 编辑状态的操作按钮 -->
					<div v-else class="flex items-center space-x-3">
						<div class="flex items-center space-x-2">
							<label class="text-sm text-gray-600 dark:text-gray-400 whitespace-nowrap">展示填写示例：</label>
							<NSwitch v-model:value="showExample" />
						</div>
						<div class="flex space-x-2">
							<NButton @click="handleDeleteChecklist" type="error" ghost>删除</NButton>
							<NButton @click="handleSaveChecklist">保存</NButton>
							<NButton @click="handlePublishChecklist" type="primary">发布</NButton>
						</div>
					</div>
				</div>

				<!-- 主要内容区 -->
				<div class="flex-1 flex min-h-0">
					<!-- 左侧条款列表 -->
					<div class="w-[20%] border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
						<div
							class="p-4 border-gray-200 dark:border-gray-700 font-medium flex-shrink-0 flex justify-between items-center">
							<span>条款列表</span>
							<NButton v-if="selectedChecklist.status === 'draft'" size="small" quaternary type="primary"
								@click="handleAddClause">
								+ 添加
							</NButton>
						</div>
						<div class="flex-1 overflow-y-auto min-h-0">
							<div v-for="clause in selectedChecklist.clauses" :key="clause.id"
								class="p-4 border-b border-gray-100 dark:border-gray-800">
								<div class="mb-1">{{ clause.title }}</div>
							</div>
						</div>
					</div>

					<!-- 右侧条款详情 -->
					<div :class="showExample ? 'w-[50%]' : 'w-[80%]'"
						class="flex flex-col h-full border-r border-gray-200 dark:border-gray-700">
						<div class="flex-1 overflow-y-auto p-4 min-h-0">
							<NCollapse v-model:expanded-names="collapseExpandedNames">
								<NCollapseItem v-for="clause in selectedChecklist.clauses" :key="clause.id"
									:name="clause.id.toString()">
									<template #header>
										<div class="flex justify-between items-center w-full">
											<div v-if="selectedChecklist.status === 'published'">
												{{ clause.title }}
											</div>
											<div v-else class="flex-1 mr-4">
												<NInput v-model:value="clause.title" placeholder="请输入条款标题" @blur="handleSaveClause"
													@click.stop />
											</div>
											<!-- 删除按钮 -->
											<div v-if="selectedChecklist.status === 'draft'" class="flex-shrink-0">
												<NPopover trigger="click" placement="bottom">
													<template #trigger>
														<NButton size="tiny" type="error" ghost circle @click.stop>
															<template #icon>
																<NIcon>
																	<TrashOutline />
																</NIcon>
															</template>
														</NButton>
													</template>
													<div class="text-center">
														<div class="mb-2">确定删除该条款下的所有内容吗？</div>
														<NSpace>
															<NButton size="small" @click="handleDeleteClause(clause.id)">
																确定
															</NButton>
														</NSpace>
													</div>
												</NPopover>
											</div>
										</div>
									</template>

									<div class="space-y-4 border-solid border border-slate-200 p-5 rounded" style="margin-top: -20px;">
										<div>
											<h4 class="font-medium mb-2 text-gray-900 dark:text-gray-100">
												条款说明
											</h4>
											<div v-if="selectedChecklist.status === 'published'">
												<div
													class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed p-3 bg-gray-50 dark:bg-gray-800 rounded">
													{{ clause.description }}
												</div>
											</div>
											<div v-else>
												<NInput v-model:value="clause.description" type="textarea" :rows="4" placeholder="请输入条款说明"
													@blur="handleSaveClause" />
											</div>
										</div>
										<div>
											<div class="flex justify-between items-center mb-2">
												<h4 class="font-medium text-gray-900 dark:text-gray-100">
													风险点
												</h4>
												<NButton v-if="selectedChecklist.status === 'draft'" size="small" quaternary type="primary"
													@click="handleAddRisk(clause)">
													+ 添加自定义风险点
												</NButton>
											</div>
											<div class="space-y-2">
												<div v-for="risk in clause.risks" :key="risk.id" class="p-3 border rounded relative group">
													<!-- 已发布状态：只读显示 -->
													<div v-if="selectedChecklist.status === 'published'">
														<div v-if="risk.enabled" class="font-medium mb-1">
															{{ risk.title }}
														</div>
														<div v-if="risk.enabled" class="text-sm">
															{{ risk.description }}
														</div>
													</div>

													<!-- 编辑状态：可编辑 -->
													<div v-else>
														<div class="flex items-start space-x-2">
															<NCheckbox v-model:checked="risk.enabled" class="mt-1" />
															<div class="flex-1">
																<div class="font-medium mb-1 cursor-pointer" @click="handleEditRisk(risk)">
																	{{ risk.title }}
																</div>
																<div class="text-sm">
																	{{ risk.description }}
																</div>
															</div>
															<div class="opacity-0 group-hover:opacity-100 transition-opacity">
																<NButton size="tiny" type="error" ghost circle
																	@click="handleDeleteRisk(clause.id, risk.id)">
																	<template #icon>
																		<NIcon>
																			<TrashOutline />
																		</NIcon>
																	</template>
																</NButton>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</NCollapseItem>
							</NCollapse>
						</div>
					</div>

					<!-- 填写示例区域 -->
					<div v-if="showExample" class="w-[30%] flex flex-col h-full">
						<div class="p-4 border-gray-200 dark:border-gray-700 font-medium flex-shrink-0">
							填写示例
						</div>
						<div class="flex-1 overflow-y-auto p-4 min-h-0">
							<div class="space-y-4">
								<div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded">
									<h4 class="font-medium text-blue-800 dark:text-blue-200 mb-2">
										条款名称：填写具体的条款名称
									</h4>
									<p class="text-sm text-blue-700 dark:text-blue-300">
										例：标的物条款
									</p>
								</div>

								<div class="bg-green-50 dark:bg-green-900/20 p-4 rounded">
									<h4 class="font-medium text-green-800 dark:text-green-200 mb-2">
										条款说明：该类条款的描述性说明，以便于模型定位该条款再合同中的内容
									</h4>
									<p class="text-sm text-green-700 dark:text-green-300">
										例：标的物条款明确描述设备采购合同的标的物，包括标的物的名称、品牌型号、规格（功能要求、核心技术参数）、数量（重量）、价格（单价）、配件、附属服务、质量标准等内容，标的物条款的最终目的是明确交付设备的数量、品牌、型号等内容。
									</p>
								</div>

								<div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded">
									<h4 class="font-medium text-orange-800 dark:text-orange-200 mb-2">
										风险点：该类条款中需要审查的一种风险名称及其具体说明，让模型能够更好地理解、识别合同条款中存在的风险点
									</h4>
									<div class="space-y-2">
										<div class="text-sm text-orange-700 dark:text-orange-300">
											<div class="font-medium">例：使用非标准化的计量单位</div>
											<div class="ml-4">
												标的物的数量使用非标准化的计量单位，如“包、箱、袋、捆、车”等；
											</div>
										</div>
										<div class="text-sm text-orange-700 dark:text-orange-300">
											<div class="font-medium">例：标的物重要信息缺失</div>
											<div class="ml-4">标的物信息中缺失“名称、规格、数量、价格”；或未将该信息作为附件；或仅约定“详见附件”“详见xx协议”，实际没有附件或附件中信息仍缺失。</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 新建审查清单弹窗 -->
		<NModal v-model:show="showCreateModal" preset="dialog" title="新建审查清单" :style="{ width: '600px' }">
			<NForm ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="150px" class="mt-4">
				<!-- 清单名称 -->
				<NFormItem label="清单名称：" path="name">
					<NInput v-model:value="formData.name" placeholder="请输入清单名称" maxlength="20" show-count />
				</NFormItem>

				<!-- 清单创建方式 -->
				<NFormItem label="清单创建方式：" path="createType">
					<NRadioGroup v-model:value="formData.createType">
						<NRadioButton value="fromExisting">从已有清单创建</NRadioButton>
						<NRadioButton value="fromBlank">从空白清单创建</NRadioButton>
					</NRadioGroup>
				</NFormItem>

				<!-- 选择清单 (仅在从已有清单创建时显示) -->
				<NFormItem v-if="formData.createType === 'fromExisting'" label="选择清单：" path="selectedTemplate">
					<NSelect v-model:value="formData.selectedTemplate" :options="templateOptions" placeholder="请选择清单" />
				</NFormItem>

				<!-- 审查立场 (仅在从已有清单创建时显示) -->
				<NFormItem v-if="formData.createType === 'fromExisting'" label="审查立场：" path="reviewStance">
					<NRadioGroup v-model:value="formData.reviewStance">
						<NRadioButton value="party_a">甲方</NRadioButton>
						<NRadioButton value="party_b">乙方</NRadioButton>
					</NRadioGroup>
				</NFormItem>

				<!-- 清单描述 -->
				<NFormItem label="清单描述：" path="description">
					<NInput v-model:value="formData.description" type="textarea" placeholder="请输入清单描述" :rows="3" />
				</NFormItem>
			</NForm>

			<template #action>
				<div class="flex justify-end space-x-2">
					<NButton @click="handleCancelCreate">取消</NButton>
					<NButton type="primary" @click="handleConfirmCreate"> 确定 </NButton>
				</div>
			</template>
		</NModal>

		<!-- 添加条款弹窗 -->
		<NModal v-model:show="showAddClauseModal" preset="dialog" title="添加条款" :style="{ width: '600px' }">
			<NForm ref="clauseFormRef" :model="clauseFormData" :rules="clauseFormRules" label-placement="left"
				label-width="100px" class="mt-4">
				<NFormItem label="条款名称：" path="title">
					<NInput v-model:value="clauseFormData.title" placeholder="请输入条款名称" maxlength="50" show-count />
				</NFormItem>

				<NFormItem label="条款说明：" path="description">
					<NInput v-model:value="clauseFormData.description" type="textarea" placeholder="请输入条款说明" :rows="4" />
				</NFormItem>
			</NForm>

			<template #action>
				<div class="flex justify-end space-x-2">
					<NButton @click="showAddClauseModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmAddClause">确定</NButton>
				</div>
			</template>
		</NModal>

		<!-- 添加风险点弹窗 -->
		<NModal v-model:show="showAddRiskModal" preset="dialog" title="添加风险点" :style="{ width: '600px' }">
			<NForm ref="riskFormRef" :model="riskFormData" :rules="riskFormRules" label-placement="left" label-width="120px"
				class="mt-4">
				<NFormItem label="风险点名称：" path="title">
					<NInput v-model:value="riskFormData.title" placeholder="请输入风险点名称" maxlength="50" show-count />
				</NFormItem>

				<NFormItem label="风险点说明：" path="description">
					<NInput v-model:value="riskFormData.description" type="textarea" placeholder="请输入风险点说明" :rows="4" />
				</NFormItem>
			</NForm>

			<template #action>
				<div class="flex justify-end space-x-2">
					<NButton @click="showAddRiskModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmAddRisk">确定</NButton>
				</div>
			</template>
		</NModal>

		<!-- 编辑风险点弹窗 -->
		<NModal v-model:show="showEditRiskModal" preset="dialog" title="编辑风险点" :style="{ width: '600px' }">
			<NForm ref="riskFormRef" :model="riskFormData" :rules="riskFormRules" label-placement="left" label-width="120px"
				class="mt-4">
				<NFormItem label="风险点名称：" path="title">
					<NInput v-model:value="riskFormData.title" placeholder="请输入风险点名称" maxlength="50" show-count />
				</NFormItem>

				<NFormItem label="风险点说明：" path="description">
					<NInput v-model:value="riskFormData.description" type="textarea" placeholder="请输入风险点说明" :rows="4" />
				</NFormItem>
			</NForm>

			<template #action>
				<div class="flex justify-end space-x-2">
					<NButton @click="showEditRiskModal = false">取消</NButton>
					<NButton type="primary" @click="handleConfirmEditRisk">确定</NButton>
				</div>
			</template>
		</NModal>
	</div>
</template>

<script setup lang="ts">
import {
	AddOutline,
	DocumentTextOutline,
	SearchOutline,
	TrashOutline,
} from "@vicons/ionicons5";
import {
	NButton,
	NCollapse,
	NCollapseItem,
	NIcon,
	NInput,
	NSelect,
	NTree,
	NModal,
	NForm,
	NFormItem,
	NRadioGroup,
	NRadioButton,
	NBadge,
	NSwitch,
	NCheckbox,
	NPopover,
	NSpace,
	useMessage,
	useDialog,
} from "naive-ui";
import { computed, ref, watch } from "vue";

const message = useMessage();
const dialog = useDialog();

// 弹窗相关
const showCreateModal = ref(false);
const showAddClauseModal = ref(false);
const showAddRiskModal = ref(false);
const showEditRiskModal = ref(false);
const formRef = ref();
const clauseFormRef = ref();
const riskFormRef = ref();

// 表单数据
const formData = ref({
	name: "",
	createType: "fromExisting",
	selectedTemplate: "",
	reviewStance: "party_a",
	description: "",
});

// 条款表单数据
const clauseFormData = ref({
	title: "",
	description: "",
});

// 风险点表单数据
const riskFormData = ref({
	title: "",
	description: "",
});

// 当前编辑的条款和风险点
const currentEditingRisk = ref<any>(null);
const currentClauseForRisk = ref<any>(null);

// 表单验证规则
const formRules = computed(() => ({
	name: [
		{ required: true, message: "请输入清单名称", trigger: "blur" },
		{ max: 20, message: "清单名称不能超过20个字符", trigger: "blur" },
	],
	createType: [
		{ required: true, message: "请选择清单创建方式", trigger: "change" },
	],
	selectedTemplate:
		formData.value.createType === "fromExisting"
			? [{ required: true, message: "请选择清单", trigger: "change" }]
			: [],
	reviewStance:
		formData.value.createType === "fromExisting"
			? [{ required: true, message: "请选择审查立场", trigger: "change" }]
			: [],
}));

// 条款表单验证规则
const clauseFormRules = {
	title: [
		{ required: true, message: "请输入条款名称", trigger: "blur" },
		{ max: 50, message: "条款名称不能超过50个字符", trigger: "blur" },
	],
	description: [
		{ required: true, message: "请输入条款说明", trigger: "blur" },
	],
};

// 风险点表单验证规则
const riskFormRules = {
	title: [
		{ required: true, message: "请输入风险点名称", trigger: "blur" },
		{ max: 50, message: "风险点名称不能超过50个字符", trigger: "blur" },
	],
	description: [
		{ required: true, message: "请输入风险点说明", trigger: "blur" },
	],
};

// 模板选项（预置清单）
const templateOptions = computed(() => {
	const presetChecklists =
		treeData.value.find((item) => item.key === "preset")?.children || [];
	return presetChecklists.map((item) => ({
		label: item.label,
		value: item.key,
	}));
});

// 监听创建方式变化，清空相关字段
watch(
	() => formData.value.createType,
	(newType) => {
		if (newType === "fromBlank") {
			formData.value.selectedTemplate = "";
			formData.value.reviewStance = "party_a";
		}
	}
);

// 搜索值
const searchValue = ref("");

// 选中的节点
const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>(["preset", "my"]);

// 审查立场选项
const reviewStance = ref("party_a");
const reviewStanceOptions = [
	{
		label: "甲方立场",
		value: "party_a",
	},
	{
		label: "乙方立场",
		value: "party_b",
	},
	{
		label: "中立立场",
		value: "neutral",
	},
];

// 选中的清单
const selectedChecklist = ref<any>(null);

// 编辑状态
const showExample = ref(false);

// Collapse展开状态
const collapseExpandedNames = ref<string[]>([]);

// 树形数据
const treeData = ref([
	{
		key: "preset",
		label: "预置清单",
		children: [
			{
				key: "preset-1",
				label: "租赁合同",
				type: "checklist",
				data: {
					title: "租赁合同",
					name: "Lease_Party_v1.2",
					description:
						"适用于房屋租赁合同的审查清单，包含租赁期限、租金支付、违约责任等关键条款",
					status: "published", // published | draft
					clauses: [
						{
							id: 1,
							title: "主体信息条款",
							category: "合同主体",
							description:
								"主体信息条款是指在合同中明确规定合同当事人的身份、资质等基本信息的条款。该条款应当包括当事人的姓名或名称、住所、联系方式等。合同主体的身份信息必须真实、准确、完整，包括但不限于法定代表人信息、营业执照、组织机构代码等相关证明文件。",
							risks: [
								{
									id: 1,
									title: "合同主体身份不明",
									description:
										"缺少对合同主体身份的明确约定，可能导致合同履行困难，无法确定责任主体",
									enabled: true,
								},
								{
									id: 2,
									title: "主体资格审查不充分",
									description:
										"未对合同主体的资格进行充分审查，可能存在主体不具备履约能力的风险",
									enabled: true,
								},
							],
						},
						{
							id: 2,
							title: "房屋信息条款",
							category: "标的物",
							description:
								"房屋信息条款应当详细描述租赁房屋的具体信息，包括房屋地址、面积、结构、用途、装修状况、配套设施等。应当明确房屋的权属状况，包括房屋所有权证书、土地使用权证书等相关证明文件。",
							risks: [
								{
									id: 3,
									title: "房屋信息描述不清",
									description: "房屋信息不够详细可能导致纠纷，影响合同履行",
								},
								{
									id: 4,
									title: "缺少房屋现状说明",
									description:
										"未对房屋现状进行详细说明，可能影响后续维修责任认定",
								},
								{
									id: 5,
									title: "房屋权属不明",
									description: "未明确房屋权属状况，可能存在权属纠纷风险",
								},
							],
						},
						{
							id: 3,
							title: "租赁期限条款",
							category: "履行期限",
							description:
								"租赁期限条款应当明确约定租赁的起始时间和终止时间，以及续租的相关约定。应当明确租赁期限的计算方式，包括起租日、免租期、装修期等具体时间节点。",
							risks: [
								{
									id: 6,
									title: "租赁期限约定不明",
									description: "租赁期限不明确可能导致合同履行纠纷",
								},
								{
									id: 7,
									title: "续租条件不清",
									description: "未明确续租的条件和程序，可能影响租赁关系的延续",
								},
							],
						},
						{
							id: 4,
							title: "租金支付条款",
							category: "价款支付",
							description:
								"租金支付条款应当明确约定租金的数额、支付方式、支付时间、逾期责任等。应当明确租金的调整机制，包括调整的条件、幅度、程序等。",
							risks: [
								{
									id: 8,
									title: "租金标准不明确",
									description: "租金数额、计算方式不明确，可能导致支付纠纷",
								},
								{
									id: 9,
									title: "支付方式约定不清",
									description: "未明确具体的支付方式和时间，可能影响资金回收",
								},
								{
									id: 10,
									title: "逾期责任不足",
									description: "逾期支付租金的违约责任约定不足，缺乏有效约束",
								},
							],
						},
						{
							id: 5,
							title: "房屋使用条款",
							category: "使用管理",
							description:
								"房屋使用条款应当明确约定房屋的使用用途、使用限制、维护保养责任等。应当明确禁止的行为和允许的改造范围。",
							risks: [
								{
									id: 11,
									title: "使用用途限制不明",
									description: "未明确房屋使用用途的限制，可能导致使用纠纷",
								},
								{
									id: 12,
									title: "维护责任不清",
									description: "房屋维护保养责任分工不明确，可能产生维修纠纷",
								},
							],
						},
						{
							id: 6,
							title: "违约责任条款",
							category: "违约责任",
							description:
								"违约责任条款应当明确约定各种违约情形及其相应的法律后果。应当设置合理的违约金标准和损失赔偿机制。",
							risks: [
								{
									id: 13,
									title: "违约责任约定不足",
									description: "违约责任条款过于宽泛或不足，缺乏有效约束力",
								},
								{
									id: 14,
									title: "违约金标准不合理",
									description: "违约金数额过高或过低，可能面临法律风险",
								},
							],
						},
						{
							id: 7,
							title: "合同解除条款",
							category: "合同终止",
							description:
								"合同解除条款应当明确约定合同解除的条件、程序、后果等。应当区分不同解除情形的处理方式。",
							risks: [
								{
									id: 15,
									title: "解除条件不明确",
									description: "合同解除的条件和程序不明确，可能导致解除纠纷",
								},
								{
									id: 16,
									title: "解除后果约定不全",
									description: "未充分约定合同解除后的财产处理和责任承担",
								},
							],
						},
					],
				},
			},
			{
				key: "preset-2",
				label: "销售合同",
				type: "checklist",
				data: {
					title: "销售合同",
					name: "Sales_Contract_v2.0",
					description:
						"适用于商品销售合同的审查清单，涵盖商品规格、价格条款、交付方式等要素",
					status: "published",
					clauses: [
						{
							id: 8,
							title: "商品规格条款",
							category: "标的物",
							description:
								"商品规格条款应当详细描述所销售商品的规格、型号、质量标准等技术参数。应当明确商品的品牌、产地、生产日期、保质期等关键信息。",
							risks: [
								{
									id: 17,
									title: "商品规格不明确",
									description: "商品规格描述不清可能导致交付纠纷",
								},
								{
									id: 18,
									title: "质量标准缺失",
									description: "未明确质量标准和检验方法，可能产生质量争议",
								},
							],
						},
						{
							id: 9,
							title: "价格条款",
							category: "价款支付",
							description:
								"价格条款应当明确约定商品的单价、总价、价格调整机制等。应当明确税费承担、运费承担等相关费用。",
							risks: [
								{
									id: 19,
									title: "价格约定不明",
									description: "价格条款不明确可能导致支付纠纷",
								},
								{
									id: 20,
									title: "费用承担不清",
									description: "相关费用承担约定不明确，可能产生额外争议",
								},
							],
						},
						{
							id: 10,
							title: "交付条款",
							category: "履行方式",
							description:
								"交付条款应当明确约定交付时间、地点、方式、验收标准等。应当明确交付风险的转移时点。",
							risks: [
								{
									id: 21,
									title: "交付时间不明确",
									description: "交付时间约定不明确可能影响合同履行",
								},
								{
									id: 22,
									title: "验收标准缺失",
									description: "缺少明确的验收标准和程序，可能产生验收纠纷",
								},
							],
						},
						{
							id: 11,
							title: "质量保证条款",
							category: "质量保证",
							description:
								"质量保证条款应当明确约定质量保证期限、保证范围、保证责任等。应当明确质量问题的处理程序。",
							risks: [
								{
									id: 23,
									title: "质保期限不明",
									description: "质量保证期限约定不明确，可能影响售后服务",
								},
								{
									id: 24,
									title: "质保责任不清",
									description: "质量保证责任范围不明确，可能产生责任纠纷",
								},
							],
						},
					],
				},
			},
			{
				key: "preset-3",
				label: "服务合同",
				type: "checklist",
				data: {
					title: "服务合同",
					name: "Service_Contract_v1.0",
					description:
						"适用于各类服务合同的审查清单，包含服务内容、服务标准、验收标准等",
					status: "published",
					clauses: [
						{
							id: 12,
							title: "服务内容条款",
							category: "服务范围",
							description:
								"服务内容条款应当明确约定服务提供方需要提供的具体服务内容和服务标准。应当详细描述服务的范围、深度、频次等具体要求。",
							risks: [
								{
									id: 25,
									title: "服务内容约定不清",
									description: "服务内容不明确可能导致服务质量纠纷",
								},
								{
									id: 26,
									title: "服务标准缺失",
									description: "缺少明确的服务标准和评价体系，难以衡量服务质量",
								},
							],
						},
						{
							id: 13,
							title: "服务期限条款",
							category: "履行期限",
							description:
								"服务期限条款应当明确约定服务的开始时间、结束时间、各阶段的时间节点等。应当明确延期的处理机制。",
							risks: [
								{
									id: 27,
									title: "服务期限不明确",
									description: "服务期限约定不明确可能影响项目进度",
								},
								{
									id: 28,
									title: "里程碑缺失",
									description: "缺少关键时间节点和里程碑，难以控制项目进度",
								},
							],
						},
						{
							id: 14,
							title: "验收条款",
							category: "验收标准",
							description:
								"验收条款应当明确约定验收的标准、程序、时间、责任等。应当明确验收不合格的处理方式。",
							risks: [
								{
									id: 29,
									title: "验收标准不明",
									description: "验收标准不明确可能导致验收纠纷",
								},
								{
									id: 30,
									title: "验收程序不规范",
									description: "验收程序不规范可能影响验收效果和争议解决",
								},
							],
						},
					],
				},
			},
		],
	},
	{
		key: "my",
		label: "我的清单",
		children: [
			{
				key: "my-1",
				label: "劳动合同审查",
				type: "checklist",
				data: {
					title: "劳动合同审查",
					name: "Labor_Contract_Custom",
					description: "自定义的劳动合同审查清单，针对公司特定需求制定",
					status: "draft",
					clauses: [
						{
							id: 15,
							title: "工作内容条款",
							category: "工作职责",
							description:
								"工作内容条款应当明确约定员工的工作岗位、工作职责和工作内容。应当详细描述岗位职责、工作标准、考核指标等。",
							risks: [
								{
									id: 31,
									title: "工作职责不明确",
									description: "工作职责约定不清可能导致劳动纠纷",
								},
								{
									id: 32,
									title: "考核标准缺失",
									description: "缺少明确的工作考核标准，可能影响绩效评估",
								},
							],
						},
						{
							id: 16,
							title: "薪酬福利条款",
							category: "薪酬待遇",
							description:
								"薪酬福利条款应当明确约定基本工资、绩效工资、奖金、福利待遇等。应当明确薪酬调整机制和支付方式。",
							risks: [
								{
									id: 33,
									title: "薪酬结构不清",
									description: "薪酬结构和计算方式不明确，可能产生薪酬争议",
								},
								{
									id: 34,
									title: "福利待遇约定不全",
									description: "福利待遇约定不完整，可能影响员工权益保障",
								},
							],
						},
						{
							id: 17,
							title: "工作时间条款",
							category: "工作安排",
							description:
								"工作时间条款应当明确约定工作时间、休息时间、加班安排等。应当符合劳动法律法规的相关规定。",
							risks: [
								{
									id: 35,
									title: "工作时间不合规",
									description: "工作时间安排不符合法律规定，可能面临法律风险",
								},
								{
									id: 36,
									title: "加班补偿不明",
									description: "加班时间和补偿标准不明确，可能产生劳动争议",
								},
							],
						},
					],
				},
			},
			{
				key: "my-2",
				label: "采购合同模板",
				type: "checklist",
				data: {
					title: "采购合同模板",
					name: "Procurement_Template",
					description:
						"企业采购合同专用审查清单，包含供应商资质、采购流程等要求",
					status: "published",
					clauses: [
						{
							id: 18,
							title: "供应商资质条款",
							category: "主体资格",
							description:
								"供应商资质条款应当明确约定供应商应具备的资质条件和证明文件。应当包括营业执照、生产许可证、质量认证等相关资质。",
							risks: [
								{
									id: 37,
									title: "供应商资质不足",
									description: "供应商资质不符合要求可能影响合同履行",
								},
								{
									id: 38,
									title: "资质审查不充分",
									description: "未充分审查供应商资质的真实性和有效性，存在风险",
								},
							],
						},
						{
							id: 19,
							title: "采购标的条款",
							category: "采购物品",
							description:
								"采购标的条款应当明确约定采购物品的名称、规格、型号、数量、质量标准等。应当明确技术参数和性能要求。",
							risks: [
								{
									id: 39,
									title: "标的描述不准确",
									description: "采购标的描述不准确可能导致交付不符合要求",
								},
								{
									id: 40,
									title: "技术要求不明确",
									description: "技术参数和性能要求不明确，可能影响产品质量",
								},
							],
						},
						{
							id: 20,
							title: "价格条款",
							category: "价格管理",
							description:
								"价格条款应当明确约定采购价格、价格构成、价格调整机制等。应当明确税费承担和支付条件。",
							risks: [
								{
									id: 41,
									title: "价格机制不合理",
									description: "价格调整机制不合理可能导致成本风险",
								},
								{
									id: 42,
									title: "支付条件不利",
									description: "支付条件对采购方不利，可能影响资金安排",
								},
							],
						},
						{
							id: 21,
							title: "交付条款",
							category: "交付管理",
							description:
								"交付条款应当明确约定交付时间、地点、方式、包装要求等。应当明确延期交付的责任和后果。",
							risks: [
								{
									id: 43,
									title: "交付时间不合理",
									description: "交付时间安排不合理可能影响生产计划",
								},
								{
									id: 44,
									title: "延期责任不明",
									description: "延期交付的责任和后果约定不明确，缺乏约束力",
								},
							],
						},
						{
							id: 22,
							title: "质量保证条款",
							category: "质量管理",
							description:
								"质量保证条款应当明确约定质量标准、检验方法、质保期限等。应当明确质量问题的处理程序和责任。",
							risks: [
								{
									id: 45,
									title: "质量标准不明确",
									description: "质量标准不明确可能导致质量纠纷",
								},
								{
									id: 46,
									title: "检验程序不完善",
									description: "检验方法和程序不完善，可能遗漏质量问题",
								},
							],
						},
					],
				},
			},
		],
	},
]);

// 过滤后的树形数据
const filteredTreeData = computed(() => {
	if (!searchValue.value) return treeData.value;

	const filterNode = (nodes: any[]): any[] => {
		return nodes.reduce((acc, node) => {
			if (node.label.includes(searchValue.value)) {
				acc.push(node);
			} else if (node.children) {
				const filteredChildren = filterNode(node.children);
				if (filteredChildren.length > 0) {
					acc.push({
						...node,
						children: filteredChildren,
					});
				}
			}
			return acc;
		}, []);
	};

	return filterNode(treeData.value);
});

// 处理节点选择
const handleSelectNode = (keys: string[]) => {
	selectedKeys.value = keys;
	if (keys.length > 0) {
		const selectedKey = keys[0];
		const findNode = (nodes: any[]): any => {
			for (const node of nodes) {
				if (node.key === selectedKey && node.type === "checklist") {
					return node.data;
				}
				if (node.children) {
					const found = findNode(node.children);
					if (found) return found;
				}
			}
			return null;
		};

		const nodeData = findNode(treeData.value);
		if (nodeData) {
			selectedChecklist.value = nodeData;
			// 自动展开所有条款面板
			collapseExpandedNames.value = nodeData.clauses.map((clause: any) => clause.id.toString());
		}
	}
};

// 处理节点展开
const handleExpandNode = (keys: string[]) => {
	expandedKeys.value = keys;
};





// 处理创建清单
const handleCreateChecklist = () => {
	showCreateModal.value = true;
	// 重置表单数据
	formData.value = {
		name: "",
		createType: "fromExisting",
		selectedTemplate: "",
		reviewStance: "party_a",
		description: "",
	};
};

// 处理取消创建
const handleCancelCreate = () => {
	showCreateModal.value = false;
};

// 处理确认创建
const handleConfirmCreate = async () => {
	try {
		await formRef.value?.validate();

		// 创建新清单
		const newChecklist = {
			key: `my-${Date.now()}`,
			label: formData.value.name,
			type: "checklist",
			data: {
				title: formData.value.name,
				name: formData.value.name,
				description: formData.value.description,
				status: "draft",
				clauses:
					formData.value.createType === "fromExisting"
						? getTemplateData(formData.value.selectedTemplate)
						: [],
			},
		};

		// 添加到"我的清单"
		const myChecklists = treeData.value.find((item) => item.key === "my");
		if (myChecklists) {
			myChecklists.children.push(newChecklist);
		}

		// 自动选中新创建的清单
		selectedKeys.value = [newChecklist.key];
		selectedChecklist.value = newChecklist.data;
		// 自动展开所有条款面板
		collapseExpandedNames.value = newChecklist.data.clauses.map((clause: any) => clause.id.toString());

		message.success("清单创建成功！");
		showCreateModal.value = false;
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 获取模板数据
const getTemplateData = (templateKey: string) => {
	const presetChecklists =
		treeData.value.find((item) => item.key === "preset")?.children || [];
	const template = presetChecklists.find((item) => item.key === templateKey);
	return template ? JSON.parse(JSON.stringify(template.data.clauses)) : [];
};

// 编辑清单相关函数
const handleEditChecklist = () => {
	if (selectedChecklist.value) {
		selectedChecklist.value.status = "draft";
	}
};

const handleDeleteChecklist = () => {
	dialog.warning({
		title: "确认删除",
		content: "确定要删除这个清单吗？删除后无法恢复。",
		positiveText: "确定",
		negativeText: "取消",
		onPositiveClick: () => {
			// 从树形数据中删除
			const myChecklists = treeData.value.find((item) => item.key === "my");
			if (myChecklists && selectedChecklist.value) {
				const index = myChecklists.children.findIndex(
					(item: any) => item.data === selectedChecklist.value
				);
				if (index > -1) {
					myChecklists.children.splice(index, 1);
					selectedChecklist.value = null;
					selectedKeys.value = [];
					message.success("清单删除成功！");
				}
			}
		},
	});
};

const handleSaveChecklist = () => {
	message.success("清单保存成功！");
};

const handlePublishChecklist = () => {
	if (selectedChecklist.value) {
		// 检查是否有条款
		if (!selectedChecklist.value.clauses || selectedChecklist.value.clauses.length === 0) {
			message.warning("请先添加条款后再发布清单");
			return;
		}

		selectedChecklist.value.status = "published";
		message.success("清单发布成功！");
	}
};

// 添加条款
const handleAddClause = () => {
	clauseFormData.value = {
		title: "",
		description: "",
	};
	showAddClauseModal.value = true;
};

// 确认添加条款
const handleConfirmAddClause = async () => {
	try {
		await clauseFormRef.value?.validate();

		const newClause = {
			id: Date.now(),
			title: clauseFormData.value.title,
			description: clauseFormData.value.description,
			risks: [],
		};

		selectedChecklist.value.clauses.push(newClause);
		// 自动展开新添加的条款
		collapseExpandedNames.value.push(newClause.id.toString());
		showAddClauseModal.value = false;
		message.success("条款添加成功！");
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 删除条款
const handleDeleteClause = (clauseId: number) => {
	const index = selectedChecklist.value.clauses.findIndex((c: any) => c.id === clauseId);
	if (index > -1) {
		selectedChecklist.value.clauses.splice(index, 1);
		// 从展开状态中移除删除的条款
		const expandedIndex = collapseExpandedNames.value.indexOf(clauseId.toString());
		if (expandedIndex > -1) {
			collapseExpandedNames.value.splice(expandedIndex, 1);
		}
		message.success("条款删除成功！");
	}
};

// 保存条款
const handleSaveClause = () => {
	// 自动保存，无需额外操作
	message.success("条款保存成功！");
};

// 添加风险点
const handleAddRisk = (clause: any) => {
	riskFormData.value = {
		title: "",
		description: "",
	};
	currentClauseForRisk.value = clause;
	showAddRiskModal.value = true;
};

// 确认添加风险点
const handleConfirmAddRisk = async () => {
	try {
		await riskFormRef.value?.validate();

		const newRisk = {
			id: Date.now(),
			title: riskFormData.value.title,
			description: riskFormData.value.description,
			enabled: true,
		};

		currentClauseForRisk.value.risks.push(newRisk);
		showAddRiskModal.value = false;
		message.success("风险点添加成功！");
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 编辑风险点
const handleEditRisk = (risk: any) => {
	riskFormData.value = {
		title: risk.title,
		description: risk.description,
	};
	currentEditingRisk.value = risk;
	showEditRiskModal.value = true;
};

// 确认编辑风险点
const handleConfirmEditRisk = async () => {
	try {
		await riskFormRef.value?.validate();

		currentEditingRisk.value.title = riskFormData.value.title;
		currentEditingRisk.value.description = riskFormData.value.description;

		showEditRiskModal.value = false;
		message.success("风险点修改成功！");
	} catch (error) {
		console.log("表单验证失败:", error);
	}
};

// 删除风险点
const handleDeleteRisk = (clauseId: number, riskId: number) => {
	const clause = selectedChecklist.value.clauses.find((c: any) => c.id === clauseId);
	if (!clause) return;

	const index = clause.risks.findIndex((r: any) => r.id === riskId);
	if (index > -1) {
		clause.risks.splice(index, 1);
		message.success("风险点删除成功！");
	}
};
</script>

<style scoped>
:deep(.n-collapse-item__header-main) {
	padding: 10px;
	background-color: rgb(246, 247, 251);
	border-radius: 5px;
	border: 1px solid #e2e8f0;
}

:deep(.n-collapse .n-collapse-item:not(:first-child)) {
	border-top: none
}

/* 树形组件样式优化 */
.tree-container :deep(.n-tree-node-content) {
	padding: 8px 12px;
	display: flex;
	align-items: center;
}

.tree-container :deep(.n-tree-node-content:hover) {
	background-color: var(--n-node-color-hover);
}

.tree-container :deep(.n-tree-node-switcher) {
	display: flex;
	align-items: center;
	justify-content: center;
}

.tree-container :deep(.n-tree-node-content__text) {
	display: flex;
	align-items: center;
}

/* 确保箭头和文字垂直居中对齐 */
.tree-container :deep(.n-tree-node) {
	display: flex;
	align-items: center;
}

.tree-container :deep(.n-tree-node-indent) {
	display: flex;
	align-items: center;
}
</style>
